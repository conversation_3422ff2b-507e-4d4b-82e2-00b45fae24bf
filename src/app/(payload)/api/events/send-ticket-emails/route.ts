import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from '@/payload-config/getPayloadConfig'
import { addQueueEmail } from '@/collections/Emails/utils'
import { EMAIL_CC } from '@/config/email'
import { Event, Order, Ticket, User } from '@/payload-types'
import { toZonedTime, format as tzFormat } from 'date-fns-tz'
import { generateEventTicketEmailHtml } from '@/mail/templates/EventTicketEmail'
import { getServerSideURL } from '@/utilities/getURL'

export const dynamic = 'force-dynamic'
export const maxDuration = 120; // 2 minutes max duration

export async function POST(req: NextRequest) {
  try {
    // Check for authorization header
    const authHeader = req.headers.get('authorization')
    const apiSecret = process.env.API_SECRET || process.env.CRON_SECRET

    if (!apiSecret || authHeader !== `Bearer ${apiSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await req.json()
    const { eventId, batchSize = 20 } = body

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    // Initialize Payload
    const payload = await getPayload()

    // Get event details
    const event = await payload.findByID({
      collection: 'events',
      id: eventId,
    }) as Event

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 })
    }

    // Format event date/time information
    const startTime = event?.startDatetime
      ? tzFormat(toZonedTime(new Date(event.startDatetime), 'Asia/Ho_Chi_Minh'), 'HH:mm')
      : ''
    const endTime = event?.endDatetime
      ? tzFormat(toZonedTime(new Date(event.endDatetime), 'Asia/Ho_Chi_Minh'), 'HH:mm')
      : ''
    const eventDate = event?.startDatetime
      ? tzFormat(toZonedTime(new Date(event.startDatetime), 'Asia/Ho_Chi_Minh'), 'dd/MM/yyyy')
      : ''
    const eventLocation = event?.eventLocation as string

    // Find tickets for this event that are booked
    const tickets = await payload.find({
      collection: 'tickets',
      where: {
        and: [
          {
            event: {
              equals: eventId,
            },
          },
          {
            status: {
              equals: 'booked',
            },
          }
        ],
      },
      depth: 2, // Include user and order information
      limit: batchSize * 5, // Get more tickets to account for multiple tickets per user
    }) as { docs: Ticket[] }

    // Filter tickets that belong to completed orders
    const validTickets = tickets.docs.filter(ticket => {
      const order = typeof ticket.order === 'object' ? ticket.order : null
      return order && order.status === 'completed'
    })

    // Group tickets by user
    const ticketsByUser = validTickets.reduce((acc, ticket) => {
      const userId = typeof ticket.user === 'object' ? ticket.user.id : ticket.user

      if (!acc[userId]) {
        acc[userId] = []
      }

      acc[userId].push(ticket)

      return acc
    }, {} as Record<string, Ticket[]>)

    // Process each user's tickets
    let emailsQueued = 0
    const baseUrl = getServerSideURL() || 'http://localhost:3000'

    for (const [userId, userTickets] of Object.entries(ticketsByUser)) {
      // Get user details if not already included in the ticket
      let user: User | null = null

      // Check if user object is already included in the first ticket
      if (typeof userTickets[0].user === 'object') {
        user = userTickets[0].user as User
      } else {
        // Fetch user details
        user = await payload.findByID({
          collection: 'users',
          id: userId,
        }) as User
      }

      if (!user || !user.email) continue

      // Process each ticket for this user
      for (const ticket of userTickets) {
          if (emailsQueued >= batchSize) {
            // Reached batch limit
            return NextResponse.json({ 
              message: `Queued ${emailsQueued} emails successfully. More tickets remain to be processed.`,
              processed: emailsQueued,
              hasMore: true
            })
          }

          const ticketCode = ticket.ticketCode || ''
          const seat = ticket.seat || ''
          const ticketUrl = `${baseUrl}/ticket/${ticketCode}`

          // Generate email HTML
          const html = generateEventTicketEmailHtml({
            ticketCode,
            seat,
            eventName: event.title || '',
            eventDate: `${startTime || 'N/A'} - ${endTime || 'N/A'}, ${eventDate || 'N/A'} (Giờ Việt Nam | Vietnam Time, GMT+7)`,
            eventLocation,
            ticketUrl,
            eventStartTime: event.startDatetime || '',
            eventEndTime: event.endDatetime || '',
            eventDateFormatted: eventDate || '',
          })

          // Queue email
          await addQueueEmail({
            payload,
            resendMailData: {
              to: user.email,
              cc: EMAIL_CC,
              subject: `Your Tickets for ${event.title}`,
              html,
            },
            emailData: {
              user: user.id,
              event: event.id,
              ticket: ticket.id,
            },
          })

          emailsQueued++
        }
      }
    }

    return NextResponse.json({ 
      message: `Queued ${emailsQueued} emails successfully`,
      processed: emailsQueued,
      hasMore: emailsQueued === 0 ? false : emailsQueued < batchSize
    })
  } catch (error) {
    console.error('Error sending event ticket emails:', error)
    return NextResponse.json({ 
      error: 'Failed to process ticket emails',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
