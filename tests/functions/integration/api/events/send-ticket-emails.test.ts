import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '../../../../../src/app/(payload)/api/events/send-ticket-emails/route'
import { Event, Ticket, User, Order } from '@/payload-types'

// Mock the getPayload function
vi.mock('../../../../../src/payload-config/getPayloadConfig', () => ({
  getPayload: vi.fn(),
}))

// Mock the email utilities
vi.mock('../../../../../src/collections/Emails/utils', () => ({
  addQueueEmail: vi.fn(),
}))

// Mock the email config
vi.mock('../../../../../src/config/email', () => ({
  EMAIL_CC: '<EMAIL>',
}))

// Mock the email template
vi.mock('../../../../../src/mail/templates/EventTicketEmail', () => ({
  generateEventTicketEmailHtml: vi.fn().mockReturnValue('<html>Mock Email</html>'),
}))

// Mock the URL utility
vi.mock('../../../../../src/utilities/getURL', () => ({
  getServerSideURL: vi.fn().mockReturnValue('http://localhost:3000'),
}))

// Import the mocked modules
import { getPayload } from '../../../../../src/payload-config/getPayloadConfig'
import { addQueueEmail } from '../../../../../src/collections/Emails/utils'
import { generateEventTicketEmailHtml } from '../../../../../src/mail/templates/EventTicketEmail'

describe('Send Ticket Emails API', () => {
  // Mock data
  const mockEvent: Event = {
    id: 1,
    title: 'Test Concert',
    eventLocation: 'Test Venue',
    startDatetime: '2024-12-01T19:00:00Z',
    endDatetime: '2024-12-01T22:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  } as Event

  const mockUser: User = {
    id: 1,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  } as User

  const mockOrder: Order = {
    id: 1,
    status: 'completed',
    orderCode: 'ORD001',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  } as Order

  const mockTickets: Ticket[] = [
    {
      id: 1,
      ticketCode: 'TK001',
      seat: 'A1',
      status: 'booked',
      event: 1,
      user: mockUser,
      order: mockOrder,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    } as Ticket,
    {
      id: 2,
      ticketCode: 'TK002',
      seat: 'A2',
      status: 'booked',
      event: 1,
      user: mockUser,
      order: mockOrder,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    } as Ticket,
  ]

  const mockPayload = {
    findByID: vi.fn(),
    find: vi.fn(),
  }

  beforeEach(() => {
    vi.resetAllMocks()
    vi.mocked(getPayload).mockResolvedValue(mockPayload as any)
    
    // Set up environment variable for testing
    process.env.CRON_SECRET = 'test-secret'
  })

  const createMockRequest = (body: any, authHeader?: string) => {
    return {
      json: vi.fn().mockResolvedValue(body),
      headers: {
        get: vi.fn().mockImplementation((header: string) => {
          if (header === 'authorization') {
            return authHeader || 'Bearer test-secret'
          }
          return null
        }),
      },
    } as unknown as NextRequest
  }

  it('should return 401 when authorization header is missing', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 1 }, undefined)

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(401)
    expect(responseData.error).toBe('Unauthorized')
  })

  it('should return 401 when authorization header is invalid', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 1 }, 'Bearer invalid-secret')

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(401)
    expect(responseData.error).toBe('Unauthorized')
  })

  it('should return 400 when eventId is missing', async () => {
    // Arrange
    const request = createMockRequest({})

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(400)
    expect(responseData.error).toBe('Event ID is required')
  })

  it('should return 404 when event is not found', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 999 })
    mockPayload.findByID.mockResolvedValue(null)

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(404)
    expect(responseData.error).toBe('Event not found')
  })

  it('should successfully queue emails for event tickets', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 1, batchSize: 20 })
    
    mockPayload.findByID.mockResolvedValue(mockEvent)
    mockPayload.find.mockResolvedValue({ docs: mockTickets })
    vi.mocked(addQueueEmail).mockResolvedValue({ id: 1 } as any)

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(responseData.processed).toBe(2)
    expect(responseData.message).toContain('Queued 2 emails successfully')
    
    // Verify that addQueueEmail was called for each ticket
    expect(addQueueEmail).toHaveBeenCalledTimes(2)
    
    // Verify that the email template was called
    expect(generateEventTicketEmailHtml).toHaveBeenCalledTimes(2)
  })

  it('should handle batch size limit correctly', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 1, batchSize: 1 })
    
    mockPayload.findByID.mockResolvedValue(mockEvent)
    mockPayload.find.mockResolvedValue({ docs: mockTickets })
    vi.mocked(addQueueEmail).mockResolvedValue({ id: 1 } as any)

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(responseData.processed).toBe(1)
    expect(responseData.hasMore).toBe(true)
    expect(responseData.message).toContain('More tickets remain to be processed')
    
    // Verify that only one email was queued due to batch limit
    expect(addQueueEmail).toHaveBeenCalledTimes(1)
  })

  it('should skip tickets with users that have no email', async () => {
    // Arrange
    const ticketsWithoutEmail = [
      {
        ...mockTickets[0],
        user: { ...mockUser, email: '' },
      },
      mockTickets[1],
    ]
    
    const request = createMockRequest({ eventId: 1 })
    
    mockPayload.findByID.mockResolvedValue(mockEvent)
    mockPayload.find.mockResolvedValue({ docs: ticketsWithoutEmail })
    vi.mocked(addQueueEmail).mockResolvedValue({ id: 1 } as any)

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(responseData.processed).toBe(1) // Only one ticket should be processed
    
    // Verify that only one email was queued (skipping the user without email)
    expect(addQueueEmail).toHaveBeenCalledTimes(1)
  })

  it('should handle errors gracefully', async () => {
    // Arrange
    const request = createMockRequest({ eventId: 1 })
    
    mockPayload.findByID.mockRejectedValue(new Error('Database error'))

    // Act
    const response = await POST(request)
    const responseData = await response.json()

    // Assert
    expect(response.status).toBe(500)
    expect(responseData.error).toBe('Failed to process ticket emails')
    expect(responseData.details).toBe('Database error')
  })
})
